# Multi-User, Multi-Session, Multi-Role Xapa AI Implementation Plan

## 🎯 Project Overview

This document outlines the comprehensive implementation plan for building a Multi-User, Multi-Session, Multi-Role Xapa AI based on the requirements specified in `multi-role-ai-agent_v0.2.md`.

## 🏗️ System Architecture Overview

```mermaid
graph TB
    subgraph "Client Layer"
        WC[Web Client]
        API[API Client]
    end
    
    subgraph "API Gateway"
        WS[WebSocket Handler]
        REST[REST API]
        AUTH[JWT Auth Middleware]
    end
    
    subgraph "Core Services"
        UM[User Manager]
        SM[Session Manager]
        AM[Agent Manager]
        RM[Role Manager]
    end
    
    subgraph "LLM Layer"
        LP[LLM Provider Interface]
        OAI[OpenAI GPT-4]
        QW[Qwen/Other Models]
    end
    
    subgraph "Storage Layer"
        PG[(PostgreSQL)]
        RD[(Redis)]
        FS[File System<br/>YAML Configs]
    end
    
    subgraph "Tools & Extensions"
        TM[Tool Manager]
        MS[Math Solver]
        CE[Code Executor]
        KB[Knowledge Base]
    end
    
    WC & API --> WS & REST
    WS & REST --> AUTH
    AUTH --> UM & SM & AM & RM
    SM --> RD
    UM & SM --> PG
    AM --> LP
    LP --> OAI & QW
    AM --> TM
    TM --> MS & CE & KB
    RM --> FS
```

## 📊 Database Schema Design

```mermaid
erDiagram
    users ||--o{ sessions : has
    users ||--o{ user_roles : has
    sessions ||--o{ messages : contains
    roles ||--o{ user_roles : assigned
    roles ||--o{ role_tools : has
    tools ||--o{ role_tools : used_by
    
    users {
        uuid id PK
        string username UK
        string email UK
        string password_hash
        timestamp created_at
        timestamp updated_at
    }
    
    sessions {
        uuid id PK
        uuid user_id FK
        string session_key UK
        string name
        string role_name FK
        json context
        boolean is_active
        timestamp created_at
        timestamp last_accessed
    }
    
    messages {
        uuid id PK
        uuid session_id FK
        string role
        text content
        json metadata
        timestamp created_at
    }
    
    roles {
        string name PK
        string display_name
        text description
        text system_prompt
        json config
        boolean is_active
        timestamp created_at
    }
    
    role_tools {
        uuid role_name FK
        uuid tool_name FK
        json permissions
    }
    
    tools {
        string name PK
        string description
        json schema
        boolean is_active
    }
```

## 📁 Project Structure

```
./
│
├── app/
│   ├── main.py                 # FastAPI application entry point
│   ├── api/
│   │   ├── __init__.py
│   │   ├── auth.py             # Authentication endpoints
│   │   ├── chat.py             # Chat REST endpoints
│   │   ├── session.py          # Session management endpoints
│   │   └── websocket.py        # WebSocket handlers
│   ├── core/
│   │   ├── __init__.py
│   │   ├── auth.py             # JWT authentication logic
│   │   ├── config.py           # Application configuration
│   │   ├── dependencies.py     # FastAPI dependencies
│   │   ├── exceptions.py       # Custom exceptions
│   │   └── security.py         # Security utilities
│   ├── models/
│   │   ├── __init__.py
│   │   ├── base.py            # SQLAlchemy base configuration
│   │   ├── user.py            # User model
│   │   ├── session.py         # Session model
│   │   ├── message.py         # Message model
│   │   └── role.py            # Role model
│   ├── schemas/
│   │   ├── __init__.py
│   │   ├── auth.py            # Auth request/response schemas
│   │   ├── chat.py            # Chat schemas
│   │   ├── session.py         # Session schemas
│   │   └── websocket.py       # WebSocket message schemas
│   ├── agent/
│   │   ├── __init__.py
│   │   ├── factory.py         # Agent factory pattern
│   │   ├── role_loader.py     # YAML role configuration loader
│   │   ├── memory_manager.py  # Session memory management
│   │   └── tools/
│   │       ├── __init__.py
│   │       ├── base.py        # Base tool interface
│   │       ├── math_tools.py  # Mathematics tools
│   │       └── code_tools.py  # Code execution tools
│   ├── session/
│   │   ├── __init__.py
│   │   ├── manager.py         # Session lifecycle management
│   │   ├── storage.py         # Redis storage interface
│   │   └── serializer.py     # Context serialization
│   ├── llm/
│   │   ├── __init__.py
│   │   ├── base.py           # Base LLM provider interface
│   │   ├── openai_provider.py # OpenAI implementation
│   │   └── streaming.py      # Streaming response handler
│   └── utils/
│       ├── __init__.py
│       ├── logger.py         # Logging configuration
│       └── validators.py     # Input validators
│
├── config/
│   └── settings.py           # Pydantic settings
├── roles/
│   └── default_roles.yaml    # Default role configurations
├── migrations/
│   └── alembic/             # Database migrations
├── tests/
│   ├── unit/
│   └── integration/
├── docker/
│   ├── Dockerfile
│   └── docker-compose.yml
├── .env.example
├── requirements.txt
├── README.md
└── docs/
    └── API.md
```

## 🚀 Implementation Phases

### Phase 1: Foundation (Week 1-2)

#### 1.1 Project Setup
- Initialize FastAPI project with proper structure
- Set up Docker Compose environment
- Configure logging and environment management
- Initialize Git repository with .gitignore

#### 1.2 Database and Core Models
```python
# Example User model
class User(Base):
    __tablename__ = "users"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    username = Column(String(50), unique=True, nullable=False)
    email = Column(String(100), unique=True, nullable=False)
    password_hash = Column(String(255), nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    sessions = relationship("Session", back_populates="user")
```

#### 1.3 Authentication System
- Implement JWT token generation and validation
- Create registration and login endpoints
- Set up password hashing with bcrypt
- Implement OAuth2PasswordBearer for FastAPI

### Phase 2: Session Management (Week 2-3)

#### 2.1 Session Manager Implementation
```python
class SessionManager:
    def __init__(self, redis_client: Redis, db: Session):
        self.redis = redis_client
        self.db = db
    
    async def create_session(
        self, 
        user_id: UUID, 
        name: str, 
        role_name: str
    ) -> SessionSchema:
        # Validate session name constraints
        # Create session in PostgreSQL
        # Initialize context in Redis
        pass
    
    async def restore_session(
        self, 
        session_key: str
    ) -> Optional[SessionContext]:
        # Load from Redis cache first
        # Fallback to PostgreSQL if needed
        pass
```

#### 2.2 Role System
- Implement YAML role loader
- Create role validation system
- Set up dynamic role assignment

### Phase 3: LLM Integration (Week 3-4)

#### 3.1 LLM Provider Abstraction
```python
from abc import ABC, abstractmethod

class LLMProvider(ABC):
    @abstractmethod
    async def generate(
        self, 
        messages: List[Message], 
        **kwargs
    ) -> AsyncGenerator[str, None]:
        pass
    
    @abstractmethod
    async def generate_complete(
        self, 
        messages: List[Message], 
        **kwargs
    ) -> str:
        pass

class OpenAIProvider(LLMProvider):
    def __init__(self, api_key: str):
        self.client = AsyncOpenAI(api_key=api_key)
    
    async def generate(
        self, 
        messages: List[Message], 
        **kwargs
    ) -> AsyncGenerator[str, None]:
        # Implement streaming response
        pass
```

#### 3.2 Agent Factory
- Implement LangChain agent initialization
- Create memory management per session
- Set up tool binding based on roles

### Phase 4: Real-time Communication (Week 4-5)

#### 4.1 WebSocket Implementation
```python
@router.websocket("/ws/chat")
async def websocket_chat(
    websocket: WebSocket,
    session_key: str,
    token: str = Query(...)
):
    # Validate JWT token
    # Load session context
    # Handle streaming messages
    try:
        await websocket.accept()
        while True:
            data = await websocket.receive_json()
            # Process message
            # Stream response
    except WebSocketDisconnect:
        # Clean up connection
        pass
```

#### 4.2 Streaming Protocol
```json
// Client -> Server
{
    "type": "chat.message",
    "content": "What is 2+2?",
    "session_key": "abc123"
}

// Server -> Client (streaming)
{
    "type": "chat.message.start",
    "message_id": "msg_123",
    "session_key": "abc123"
}

{
    "type": "chat.message.chunk",
    "content": "The answer",
    "message_id": "msg_123"
}

{
    "type": "chat.message.end",
    "message_id": "msg_123",
    "usage": {
        "total_tokens": 150,
        "prompt_tokens": 50,
        "completion_tokens": 100
    }
}
```

### Phase 5: Tools & Testing (Week 5-6)

#### 5.1 Tool Framework
```python
class BaseTool(ABC):
    name: str
    description: str
    
    @abstractmethod
    async def execute(self, *args, **kwargs) -> Any:
        pass

class MathSolverTool(BaseTool):
    name = "math_solver"
    description = "Solves mathematical equations"
    
    async def execute(self, equation: str) -> str:
        # Implement math solving logic
        pass
```

#### 5.2 Testing Strategy
- Unit tests for all core components
- Integration tests for API endpoints
- WebSocket connection tests
- Load testing for concurrent users

## 🔧 Technical Specifications

### Session Naming Constraints
- Length: 3-100 characters
- Allowed characters: alphanumeric and spaces
- Must be unique per user
- Validation regex: `^[a-zA-Z0-9\s]{3,100}$`

### Role Configuration Format
```yaml
# roles/default_roles.yaml
teacher:
  display_name: "Math Teacher"
  description: "Expert mathematics educator"
  system_prompt: |
    You are an experienced mathematics teacher. 
    Explain concepts step by step and encourage student thinking.
    Use examples and visual representations when helpful.
  tools:
    - math_solver
    - knowledge_search
  config:
    model: "gpt-4"
    temperature: 0.7
    max_tokens: 2000
    streaming: true

programmer:
  display_name: "Programming Assistant"
  description: "Expert software developer"
  system_prompt: |
    You are an experienced programmer proficient in multiple languages.
    Provide clean, well-documented code with explanations.
  tools:
    - code_executor
    - documentation_search
  config:
    model: "gpt-4"
    temperature: 0.3
    max_tokens: 3000
    streaming: true
```

### Security Considerations
1. **Authentication**
   - JWT tokens with 24-hour expiration
   - Refresh token mechanism
   - Rate limiting: 100 requests per minute per user

2. **Input Validation**
   - Sanitize all user inputs
   - Message length limits: 4000 characters
   - File upload restrictions (if implemented)

3. **Tool Execution**
   - Sandboxed code execution
   - Resource limits (CPU, memory, time)
   - Whitelist allowed operations

### Environment Configuration
```env
# .env.example
# Application
APP_NAME=AI_Agent_System
APP_ENV=development
DEBUG=true

# Database
DATABASE_URL=postgresql://user:password@localhost:5432/agent_db
REDIS_URL=redis://localhost:6379/0

# OpenAI
OPENAI_API_KEY=your_api_key_here
OPENAI_MODEL=gpt-4

# Security
SECRET_KEY=your-secret-key-here
JWT_ALGORITHM=HS256
JWT_EXPIRE_HOURS=24

# Limits
MAX_SESSIONS_PER_USER=10
MAX_MESSAGES_PER_SESSION=1000
MAX_MESSAGE_LENGTH=4000
```

### Docker Deployment
```yaml
# docker-compose.yml
version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: docker/Dockerfile
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=**********************************/agent_db
      - REDIS_URL=redis://redis:6379/0
      - OPENAI_API_KEY=${OPENAI_API_KEY}
    depends_on:
      - db
      - redis
    volumes:
      - ./app:/app
      - ./roles:/app/roles

  db:
    image: postgres:15
    environment:
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=password
      - POSTGRES_DB=agent_db
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes

volumes:
  postgres_data:
  redis_data:
```

## 📚 API Endpoints

### Authentication
- `GET /api/v1/auth/verify` - Verify external JWT token and get user info
- `POST /api/v1/auth/anonymous` - Get anonymous access token

### Session Management
- `GET /api/v1/sessions` - List user sessions
- `POST /api/v1/sessions` - Create new session
- `GET /api/v1/sessions/{session_key}` - Get session details
- `PUT /api/v1/sessions/{session_key}` - Update session (rename)
- `DELETE /api/v1/sessions/{session_key}` - Delete session

### Chat
- `POST /api/v1/chat` - Send message (non-streaming)
- `GET /api/v1/chat/{session_key}/history` - Get message history
- `WS /ws/chat` - WebSocket endpoint for streaming chat

### Roles
- `GET /api/v1/roles` - List available roles
- `GET /api/v1/roles/{role_name}` - Get role details

## 🎯 Success Metrics

1. **Performance**
   - API response time < 200ms (excluding LLM calls)
   - WebSocket latency < 50ms
   - Support 100+ concurrent users

2. **Reliability**
   - 99.9% uptime
   - Graceful error handling
   - Automatic session recovery

3. **User Experience**
   - Smooth streaming responses
   - Context preservation across sessions
   - Intuitive session management

## 📝 Next Steps

1. Set up development environment
2. Initialize project structure
3. Begin Phase 1 implementation
4. Set up CI/CD pipeline
5. Create monitoring dashboard

---

This implementation plan provides a comprehensive roadmap for building the Multi-User, Multi-Session, Multi-Role Xapa AI. The modular architecture ensures scalability and maintainability while meeting all specified requirements.